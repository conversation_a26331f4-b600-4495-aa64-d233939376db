package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.data.BuDataAws;

/**
 * AWS气象数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface BuDataAwsMapper 
{
    /**
     * 查询AWS气象数据
     * 
     * @param id AWS气象数据主键
     * @return AWS气象数据
     */
    public BuDataAws selectBuDataAwsById(Long id);

    /**
     * 查询AWS气象数据列表
     * 
     * @param buDataAws AWS气象数据
     * @return AWS气象数据集合
     */
    public List<BuDataAws> selectBuDataAwsList(BuDataAws buDataAws);

    /**
     * 新增AWS气象数据
     * 
     * @param buDataAws AWS气象数据
     * @return 结果
     */
    public int insertBuDataAws(BuDataAws buDataAws);

    /**
     * 修改AWS气象数据
     * 
     * @param buDataAws AWS气象数据
     * @return 结果
     */
    public int updateBuDataAws(BuDataAws buDataAws);

    /**
     * 删除AWS气象数据
     * 
     * @param id AWS气象数据主键
     * @return 结果
     */
    public int deleteBuDataAwsById(Long id);

    /**
     * 批量删除AWS气象数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataAwsByIds(Long[] ids);
}
