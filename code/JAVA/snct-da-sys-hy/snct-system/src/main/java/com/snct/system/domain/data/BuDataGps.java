package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * GPS数据对象 bu_data_gps
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public class BuDataGps extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** SN码 */
    @Excel(name = "SN码")
    private String sn;

    /** 设备ID */
    @Excel(name = "设备ID")
    private Long deviceId;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String deviceCode;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** UTC时间 */
    @Excel(name = "UTC时间")
    private String utcTime;

    /** 纬度半球 */
    @Excel(name = "纬度半球")
    private String latitudeHemisphere;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 经度半球 */
    @Excel(name = "经度半球")
    private String longitudeHemisphere;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public void setDeviceId(Long deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setUtcTime(String utcTime) 
    {
        this.utcTime = utcTime;
    }

    public String getUtcTime() 
    {
        return utcTime;
    }

    public void setLatitudeHemisphere(String latitudeHemisphere) 
    {
        this.latitudeHemisphere = latitudeHemisphere;
    }

    public String getLatitudeHemisphere() 
    {
        return latitudeHemisphere;
    }

    public void setLatitude(String latitude) 
    {
        this.latitude = latitude;
    }

    public String getLatitude() 
    {
        return latitude;
    }

    public void setLongitudeHemisphere(String longitudeHemisphere) 
    {
        this.longitudeHemisphere = longitudeHemisphere;
    }

    public String getLongitudeHemisphere() 
    {
        return longitudeHemisphere;
    }

    public void setLongitude(String longitude) 
    {
        this.longitude = longitude;
    }

    public String getLongitude() 
    {
        return longitude;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sn", getSn())
            .append("deviceId", getDeviceId())
            .append("deviceCode", getDeviceCode())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("utcTime", getUtcTime())
            .append("latitudeHemisphere", getLatitudeHemisphere())
            .append("latitude", getLatitude())
            .append("longitudeHemisphere", getLongitudeHemisphere())
            .append("longitude", getLongitude())
            .toString();
    }
}
