package com.snct.device.service;

import com.snct.common.utils.DateUtils;
import com.snct.dctcore.commoncore.domain.hbase.AwsHbaseVo;
import com.snct.dctcore.commoncore.domain.hbase.GpsHbaseVo;
import com.snct.system.domain.Device;
import com.snct.system.domain.data.BuDataAws;
import com.snct.system.domain.data.BuDataGps;
import com.snct.system.service.IBuDataAwsService;
import com.snct.system.service.IBuDataGpsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName: StoreService
 * @Description: 存储服务 - 负责将设备数据保存到MySQL数据库
 * @author: wzewei
 * @date: 2025-09-09 13:57
 */
@Service
public class StoreService {

    private static final Logger logger = LoggerFactory.getLogger(StoreService.class);

    @Autowired
    private IBuDataGpsService buDataGpsService;

    @Autowired
    private IBuDataAwsService buDataAwsService;

    /**
     * 存储GPS数据到数据库
     */
    public void storeGpsData(GpsHbaseVo gpsHbaseVo, Device device) {
        storeDeviceData(gpsHbaseVo, device, "GPS",
            () -> buDataGpsService.insertBuDataGps((BuDataGps) convertToEntity(gpsHbaseVo, device, BuDataGps.class)));
    }

    /**
     * 存储气象站数据到数据库
     */
    public void storeAwsData(AwsHbaseVo awsHbaseVo, Device device) {
        storeDeviceData(awsHbaseVo, device, "气象站",
            () -> buDataAwsService.insertBuDataAws((BuDataAws) convertToEntity(awsHbaseVo, device, BuDataAws.class)));
    }

    /**
     * 通用设备数据存储方法
     */
    private void storeDeviceData(Object hbaseVo, Device device, String deviceType, java.util.function.Supplier<Integer> saveAction) {
        if (hbaseVo == null || device == null) {
            logger.warn("{}数据或设备对象为空，跳过数据库存储", deviceType);
            return;
        }

        try {
            int result = saveAction.get();
            if (result > 0) {
                logger.debug("{}数据保存成功，设备[{}]", deviceType, device.getCode());
            } else {
                logger.warn("{}数据保存失败，设备[{}]", deviceType, device.getCode());
            }
        } catch (Exception e) {
            logger.error("保存{}数据到数据库失败，设备[{}]", deviceType, device.getCode(), e);
        }
    }

    /**
     * 通用对象转换方法
     */
    private Object convertToEntity(Object hbaseVo, Device device, Class<?> entityClass) {
        try {
            Object entity = entityClass.getDeclaredConstructor().newInstance();

            // 使用BeanUtils复制相同字段
            BeanUtils.copyProperties(hbaseVo, entity);

            // 设置设备相关信息
            setDeviceInfo(entity, device);

            // 处理时间字段
            setTimeInfo(entity, hbaseVo);

            // 设置创建时间
            setCreateTime(entity);

            return entity;
        } catch (Exception e) {
            logger.error("对象转换失败", e);
            throw new RuntimeException("对象转换失败", e);
        }
    }

    /**
     * 设置设备信息
     */
    private void setDeviceInfo(Object entity, Device device) {
        try {
            entity.getClass().getMethod("setSn", String.class).invoke(entity, device.getSn());
            entity.getClass().getMethod("setDeviceId", Long.class).invoke(entity, device.getId());
            entity.getClass().getMethod("setDeviceCode", String.class).invoke(entity, device.getCode());
        } catch (Exception e) {
            logger.debug("设置设备信息失败", e);
        }
    }

    /**
     * 设置时间信息
     */
    private void setTimeInfo(Object entity, Object hbaseVo) {
        try {
            Object initialTimeObj = hbaseVo.getClass().getMethod("getInitialTime").invoke(hbaseVo);
            if (initialTimeObj != null) {
                Long initialTime = Long.parseLong(initialTimeObj.toString());
                entity.getClass().getMethod("setInitialTime", Long.class).invoke(entity, initialTime);
                entity.getClass().getMethod("setInitialBjTime", Date.class).invoke(entity, new Date(initialTime));
            }
        } catch (Exception e) {
            logger.debug("设置时间信息失败", e);
        }
    }

    /**
     * 设置创建时间
     */
    private void setCreateTime(Object entity) {
        try {
            entity.getClass().getMethod("setCreateTime", Date.class).invoke(entity, DateUtils.getNowDate());
        } catch (Exception e) {
            logger.debug("设置创建时间失败", e);
        }
    }
}
