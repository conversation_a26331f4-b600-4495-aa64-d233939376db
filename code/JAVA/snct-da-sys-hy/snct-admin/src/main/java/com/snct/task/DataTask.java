//package com.snct.task;
//
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//
//import com.snct.utils.DeviceRawDataFileUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import com.snct.common.utils.StringUtils;
//import com.snct.system.service.impl.SysConfigServiceImpl;
//
///**
// * 数据清理定时任务
// * 用于定时清理数据库中的设备数据，保留指定天数内的数据
// *
// * <AUTHOR>
// */
//@Component("dataTask")
//public class DataTask {
//
//    private static final Logger logger = LoggerFactory.getLogger(DataTask.class);
//
//    /**
//     * 系统配置服务，用于读取配置参数
//     */
//    @Autowired
//    private SysConfigServiceImpl sysConfigService;
//
//    /**
//     * 各种设备数据Mapper
//     */
//    @Autowired
//    private BuDataPduMapper buDataPduMapper;
//
//    @Autowired
//    private BuDataAwsMapper buDataAwsMapper;
//
//    @Autowired
//    private BuDataGpsMapper buDataGpsMapper;
//
//    @Autowired
//    private BuDataModemMapper buDataModemMapper;
//
//    @Autowired
//    private BuDataAttitudeMapper buDataAttitudeMapper;
//
//    @Autowired
//    private BuDataAmplifierMapper buDataAmplifierMapper;
//
//    /**
//     * 数据库保留时间配置参数键名
//     */
//    private static final String DATABASE_KEEP_TIME_KEY = "database_keep_time";
//
//    /**
//     * 默认保留天数（如果配置不存在或无效）
//     */
//    private static final int DEFAULT_KEEP_DAYS = 30;
//
//    /**
//     * 数据清理定时任务
//     * 每天凌晨1点执行一次数据清理
//     */
//    //@Scheduled(cron = "0 0 1 * * ?")
//    public void cleanOldData() {
//        logger.info(">>> >>> >>> 开始执行数据清理任务 >>> >>> >>>");
//
//        try {
//            // 获取保留天数配置
//            int keepDays = getKeepDays();
//            logger.info("数据保留天数配置：{} 天", keepDays);
//
//            // 计算截止日期
//            LocalDate cutoffDate = LocalDate.now().minusDays(keepDays);
//            String cutoffDateStr = cutoffDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//            logger.info("数据清理截止日期：{}", cutoffDateStr);
//
//            // 执行各个数据表的清理
//            int totalDeleted = 0;
//
//            totalDeleted += cleanPduData(cutoffDateStr);
//            totalDeleted += cleanAwsData(cutoffDateStr);
//            totalDeleted += cleanGpsData(cutoffDateStr);
//            totalDeleted += cleanModemData(cutoffDateStr);
//            totalDeleted += cleanAttitudeData(cutoffDateStr);
//            totalDeleted += cleanAmplifierData(cutoffDateStr);
//
//            logger.info("数据清理任务完成，共删除 {} 条记录", totalDeleted);
//            int d = DeviceRawDataFileUtil.cleanupOldData(keepDays);
//            logger.info("清理原始数据文件完成，删除文件数量: {}", d);
//
//        } catch (Exception e) {
//            logger.error("数据清理任务执行异常", e);
//        }
//    }
//
//    /**
//     * 获取数据保留天数配置
//     *
//     * @return 保留天数
//     */
//    private int getKeepDays() {
//        try {
//            String keepTimeStr = sysConfigService.selectConfigByKey(DATABASE_KEEP_TIME_KEY);
//            if (StringUtils.isNotEmpty(keepTimeStr)) {
//                int keepDays = Integer.parseInt(keepTimeStr.trim());
//                if (keepDays > 0) {
//                    return keepDays;
//                } else {
//                    logger.warn("配置的保留天数无效：{}，使用默认值：{}", keepDays, DEFAULT_KEEP_DAYS);
//                }
//            } else {
//                logger.warn("未找到数据保留天数配置：{}，使用默认值：{}", DATABASE_KEEP_TIME_KEY, DEFAULT_KEEP_DAYS);
//            }
//        } catch (NumberFormatException e) {
//            logger.error("数据保留天数配置格式错误，使用默认值：{}", DEFAULT_KEEP_DAYS, e);
//        } catch (Exception e) {
//            logger.error("读取数据保留天数配置异常，使用默认值：{}", DEFAULT_KEEP_DAYS, e);
//        }
//
//        return DEFAULT_KEEP_DAYS;
//    }
//
//    /**
//     * 清理PDU设备数据
//     *
//     * @param cutoffDate 截止日期
//     * @return 删除的记录数
//     */
//    private int cleanPduData(String cutoffDate) {
//        try {
//            int deleted = buDataPduMapper.deleteBuDataPduByDate(cutoffDate);
//            logger.info("清理PDU设备数据：删除 {} 条记录", deleted);
//            return deleted;
//        } catch (Exception e) {
//            logger.error("清理PDU设备数据异常", e);
//            return 0;
//        }
//    }
//
//    /**
//     * 清理气象站设备数据
//     *
//     * @param cutoffDate 截止日期
//     * @return 删除的记录数
//     */
//    private int cleanAwsData(String cutoffDate) {
//        try {
//            int deleted = buDataAwsMapper.deleteBuDataAwsByDate(cutoffDate);
//            logger.info("清理气象站设备数据：删除 {} 条记录", deleted);
//            return deleted;
//        } catch (Exception e) {
//            logger.error("清理气象站设备数据异常", e);
//            return 0;
//        }
//    }
//
//    /**
//     * 清理GPS/北斗设备数据
//     *
//     * @param cutoffDate 截止日期
//     * @return 删除的记录数
//     */
//    private int cleanGpsData(String cutoffDate) {
//        try {
//            int deleted = buDataGpsMapper.deleteBuDataGpsByDate(cutoffDate);
//            logger.info("清理GPS/北斗设备数据：删除 {} 条记录", deleted);
//            return deleted;
//        } catch (Exception e) {
//            logger.error("清理GPS/北斗设备数据异常", e);
//            return 0;
//        }
//    }
//
//    /**
//     * 清理Modem设备数据
//     *
//     * @param cutoffDate 截止日期
//     * @return 删除的记录数
//     */
//    private int cleanModemData(String cutoffDate) {
//        try {
//            int deleted = buDataModemMapper.deleteBuDataModemByDate(cutoffDate);
//            logger.info("清理Modem设备数据：删除 {} 条记录", deleted);
//            return deleted;
//        } catch (Exception e) {
//            logger.error("清理Modem设备数据异常", e);
//            return 0;
//        }
//    }
//
//    /**
//     * 清理姿态设备数据
//     *
//     * @param cutoffDate 截止日期
//     * @return 删除的记录数
//     */
//    private int cleanAttitudeData(String cutoffDate) {
//        try {
//            int deleted = buDataAttitudeMapper.deleteBuDataAttitudeByDate(cutoffDate);
//            logger.info("清理姿态设备数据：删除 {} 条记录", deleted);
//            return deleted;
//        } catch (Exception e) {
//            logger.error("清理姿态设备数据异常", e);
//            return 0;
//        }
//    }
//
//    /**
//     * 清理功放设备数据
//     *
//     * @param cutoffDate 截止日期
//     * @return 删除的记录数
//     */
//    private int cleanAmplifierData(String cutoffDate) {
//        try {
//            int deleted = buDataAmplifierMapper.deleteBuDataAmplifierByDate(cutoffDate);
//            logger.info("清理功放设备数据：删除 {} 条记录", deleted);
//            return deleted;
//        } catch (Exception e) {
//            logger.error("清理功放设备数据异常", e);
//            return 0;
//        }
//    }
//
//
//    /**
//     * 清理原始数据文件
//     *
//     * @param keepDays 保留天数
//     * @return 删除的记录数
//     */
//    private int cleanRawData(int keepDays) {
//        return DeviceRawDataFileUtil.cleanupOldData(keepDays);
//    }
//}
